import { CodeBlock } from "@/components/site/code-block";
import { ComponentRenderer } from "@/components/site/component-renderer";
import { Cli } from "@/components/site/cli";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/site/tabs.tsx";
import Dock from "./dock.tsx"   


<Tabs defaultValue="preview" className="">
  <TabsList>
    <TabsTrigger value="preview">Preview</TabsTrigger>
    <TabsTrigger value="code">Code</TabsTrigger>
  </TabsList>
  <TabsContent value="preview">
    <ComponentRenderer component={<Dock />} />
  </TabsContent>
  <TabsContent value="code">
    <CodeBlock filePath="src/app/docs/dock/dock.tsx" />
  </TabsContent>
</Tabs>

# 🚀 Installation
You can add this component to your project using the CLI or by manually copying the code.

<Tabs defaultValue="cli" className="">
  <TabsList>
    <TabsTrigger value="cli">CLI</TabsTrigger>
    <TabsTrigger value="manual">Manual</TabsTrigger>
  </TabsList>
  <TabsContent value="cli">
    <Cli command={`add dock.json`} />
  </TabsContent>

  <TabsContent value="manual">
    <CodeBlock filePath="src/app/docs/dock/dock.tsx" />
  </TabsContent>
</Tabs>
